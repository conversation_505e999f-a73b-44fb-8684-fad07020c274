"use client";
import { useState, useEffect } from "react";
import { DataTable } from "@/components/shared/data-table";
import { toast } from "sonner";
import { adminComplaintColumns } from "./columns";
import { ComplaintOverview, getComplaintAdmin, updateComplaintStatus, updateComplaintPriority } from "@/api/complainAPI";
import { Tabs, TabsList, TabsTrigger } from "../ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getErrorMessage } from "@/lib/utils";
import { DataTableSkeleton } from "../shared/skeleton/skeleton-table";

export enum ComplaintStatus {
  PENDING = "pending",
  INVESTIGATING = "investigating",
  RESOLVED = "resolved",
  REJECTED = "rejected",
}

export enum ComplaintPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export enum ComplaintType {
  VIDEO_QUALITY = "video_quality",
  DELIVERY_TIME = "delivery_time",
  CONTENT_ISSUE = "content_issue",
  PAYMENT_ISSUE = "payment_issue",
  OTHER = "other",
}

export default function AdminComplaintTable() {
  const [data, setData] = useState<ComplaintOverview[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [priorityFilter, setPriorityFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const response = await getComplaintAdmin({
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        priority: priorityFilter || undefined,
        complaintType: typeFilter || undefined,
      });
      setData(response.data);
      setTotalItems(response.meta.total);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh sách khiếu nại');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, searchTerm, statusFilter, priorityFilter, typeFilter]);

  console.log("data", data?.data);
  const toolbarComponent = (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Trạng thái:</span>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Tất cả trạng thái" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=" ">Tất cả trạng thái</SelectItem>
              <SelectItem value={ComplaintStatus.PENDING}>Chờ xử lý</SelectItem>
              <SelectItem value={ComplaintStatus.INVESTIGATING}>
                Đang điều tra
              </SelectItem>
              <SelectItem value={ComplaintStatus.RESOLVED}>
                Đã giải quyết
              </SelectItem>
              <SelectItem value={ComplaintStatus.REJECTED}>
                Đã từ chối
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Mức độ ưu tiên:</span>
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Tất cả mức độ" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=" ">Tất cả mức độ</SelectItem>
              <SelectItem value={ComplaintPriority.LOW}>Thấp</SelectItem>
              <SelectItem value={ComplaintPriority.MEDIUM}>
                Trung bình
              </SelectItem>
              <SelectItem value={ComplaintPriority.HIGH}>Cao</SelectItem>
              <SelectItem value={ComplaintPriority.URGENT}>Khẩn cấp</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Loại khiếu nại:</span>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Tất cả loại" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=" ">Tất cả loại</SelectItem>
              <SelectItem value={ComplaintType.VIDEO_QUALITY}>
                Chất lượng video
              </SelectItem>
              <SelectItem value={ComplaintType.DELIVERY_TIME}>
                Thời gian giao hàng
              </SelectItem>
              <SelectItem value={ComplaintType.CONTENT_ISSUE}>
                Vấn đề nội dung
              </SelectItem>
              <SelectItem value={ComplaintType.PAYMENT_ISSUE}>
                Vấn đề thanh toán
              </SelectItem>
              <SelectItem value={ComplaintType.OTHER}>Khác</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col">
      <div className="mb-2 flex items-center justify-between space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">
          Danh sách khiếu nại
        </h1>
      </div>
      <div className="py-5">
        <DataTable
          columns={adminComplaintColumns}
          data={(data?.data as any) || []}
          manualPagination={true}
          totalItems={1 || 0}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      </div>
    </div>
  );
}
