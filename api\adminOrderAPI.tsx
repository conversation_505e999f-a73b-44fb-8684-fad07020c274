import api from "./axios"
import { Paginator } from "./models/paginator"

export interface OrderAdminDto {
    id: number
    orderId: string
    customerName: string
    customerEmail: string
    customerId: number
    talentName: string
    talentAvatarUrl?: string
    talentId: number
    serviceName: string
    serviceId: number
    orderDate: string
    completedDate?: string
    status: 'pending' | 'processing' | 'completed' | 'refunded' | 'rejected' | 'complaint'
    price: number
    currency: 'VND' | 'USD'
    paymentMethod: 'vnpay' | 'momo' | 'apple_pay' | 'credit_card' | 'other'
    isVideoSent: boolean
    isComplaint: boolean
    rating?: number
    review?: string
    internalNote?: string
    lastUpdated: string
}

export interface AdminOrderParams {
    page?: number
    limit?: number
    search?: string
    status?: string
    startDate?: string
    endDate?: string
}

export const getAdminOrders = async (params: AdminOrderParams): Promise<Paginator<OrderAdminDto>> => {
    const response = await api.get<Paginator<OrderAdminDto>>('/admin/orders', { params })
    return response.data
}

export const updateOrderStatus = async (orderId: number, status: string): Promise<OrderAdminDto> => {
    const response = await api.patch<OrderAdminDto>(`/admin/orders/${orderId}/status`, { status })
    return response.data
}

export const getOrderDetails = async (orderId: number): Promise<OrderAdminDto> => {
    const response = await api.get<OrderAdminDto>(`/admin/orders/${orderId}`)
    return response.data
}

export const addOrderNote = async (orderId: number, note: string): Promise<OrderAdminDto> => {
    const response = await api.patch<OrderAdminDto>(`/admin/orders/${orderId}/note`, { internalNote: note })
    return response.data
}