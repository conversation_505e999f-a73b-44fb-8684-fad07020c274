import api from "./axios"

export interface DashboardStats {
  totalUsers: number
  totalTalents: number
  totalOrders: number
  totalRevenue: number
  pendingComplaints: number
  pendingApprovals: number
  monthlyGrowth: number
  activeUsers: number
  completedOrders: number
  averageRating: number
}

export interface RecentActivity {
  id: string
  user: string
  action: string
  time: string
  type: 'order' | 'user' | 'talent' | 'complaint'
  avatar?: string
}

export interface PendingApproval {
  id: string
  name: string
  email: string
  category: string
  type: 'talent' | 'business'
  submittedAt: string
  avatar?: string
}

export interface RecentComplaint {
  id: string
  orderId: string
  customer: string
  talent: string
  reason: string
  status: 'pending' | 'investigating' | 'resolved' | 'rejected'
  submittedAt: string
}

export interface DashboardData {
  stats: DashboardStats
  recentActivity: RecentActivity[]
  pendingApprovals: PendingApproval[]
  recentComplaints: RecentComplaint[]
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
  const response = await api.get<DashboardStats>('/admin/dashboard/stats')
  return response.data
}

export const getRecentActivity = async (limit: number = 10): Promise<RecentActivity[]> => {
  const response = await api.get<RecentActivity[]>(`/admin/dashboard/recent-activity?limit=${limit}`)
  return response.data
}

export const getPendingApprovals = async (limit: number = 10): Promise<PendingApproval[]> => {
  const response = await api.get<PendingApproval[]>(`/admin/dashboard/pending-approvals?limit=${limit}`)
  return response.data
}

export const getRecentComplaints = async (limit: number = 10): Promise<RecentComplaint[]> => {
  const response = await api.get<RecentComplaint[]>(`/admin/dashboard/recent-complaints?limit=${limit}`)
  return response.data
}

export const getDashboardData = async (): Promise<DashboardData> => {
  const response = await api.get<DashboardData>('/admin/dashboard')
  return response.data
}

export const approveApplication = async (applicationId: string, type: 'talent' | 'business'): Promise<void> => {
  await api.post(`/admin/dashboard/approve/${type}/${applicationId}`)
}

export const rejectApplication = async (applicationId: string, type: 'talent' | 'business', reason?: string): Promise<void> => {
  await api.post(`/admin/dashboard/reject/${type}/${applicationId}`, { reason })
}
