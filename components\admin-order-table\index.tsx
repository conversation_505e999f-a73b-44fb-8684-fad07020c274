import { useState, useEffect } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { toast } from 'sonner'
import { adminTableColumns } from './columns'
import { getAdminOrders, OrderAdminDto, updateOrderStatus } from '@/api/adminOrderAPI'
import { Tabs, TabsList, TabsTrigger } from '../ui/tabs'
import { getErrorMessage } from '@/lib/utils'
import { DataTableSkeleton } from '../shared/skeleton/skeleton-table'

export default function AdminOrderTable() {
  const [data, setData] = useState<OrderAdminDto[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderStatus, setOrderStatus] = useState('tat-ca');
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const response = await getAdminOrders({
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        status: orderStatus === 'tat-ca' ? undefined : orderStatus
      })
      setData(response.data)
      setTotalItems(response.meta.total)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error(getErrorMessage(error) || 'Không thể tải danh sách đơn hàng')
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm, orderStatus])

  const handleStatusUpdate = async (orderId: number, newStatus: string) => {
    try {
      const updatedOrder = await updateOrderStatus(orderId, newStatus);
      setData(prev => prev.map(order => order.id === orderId ? updatedOrder : order));
      toast.success('Cập nhật trạng thái đơn hàng thành công');
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể cập nhật trạng thái đơn hàng');
    }
  };

  const toolbarComponent = (
    <div className="flex items-center justify-between">
       <Tabs defaultValue="tat-ca" value={orderStatus} onValueChange={setOrderStatus}>
        <TabsList className="flex flex-wrap gap-2">
          <TabsTrigger value="tat-ca">Tất cả</TabsTrigger>
          <TabsTrigger value="pending">Chờ xử lý</TabsTrigger>
          <TabsTrigger value="processing">Đang xử lý</TabsTrigger>
          <TabsTrigger value="completed">Hoàn thành</TabsTrigger>
          <TabsTrigger value="complaint">Khiếu nại</TabsTrigger>
          <TabsTrigger value="refunded">Hoàn tiền</TabsTrigger>
          <TabsTrigger value="rejected">Từ chối</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  )

  return (
      <div className='flex flex-col'>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Danh sách đơn hàng</h1>
        </div>
        {isLoading ? (
          <DataTableSkeleton />
        ) : (
          <DataTable
            columns={adminTableColumns({ onStatusUpdate: handleStatusUpdate })}
            data={data}
            manualPagination={true}
            totalItems={totalItems}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
            onSearchChange={setSearchTerm}
            toolbarComponent={toolbarComponent}
          />
        )}
      </div>
  )
}