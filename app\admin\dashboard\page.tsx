"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Users, ShoppingBag, DollarSign, AlertTriangle, Clock, Star, TrendingUp } from "lucide-react"
import Link from "next/link"
import { getDashboardData, approveApplication, rejectApplication, DashboardData } from "@/api/adminDashboardAPI"
import { toast } from "sonner"
import { getErrorMessage } from "@/lib/utils"

export default function AdminDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)
      const data = await getDashboardData()
      setDashboardData(data)
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải dữ liệu dashboard')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const handleApprove = async (applicationId: string, type: 'talent' | 'business') => {
    try {
      await approveApplication(applicationId, type)
      toast.success('Duyệt thành công')
      fetchDashboardData() // Refresh data
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể duyệt đơn')
    }
  }

  const handleReject = async (applicationId: string, type: 'talent' | 'business') => {
    try {
      await rejectApplication(applicationId, type)
      toast.success('Từ chối thành công')
      fetchDashboardData() // Refresh data
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể từ chối đơn')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-500">Không thể tải dữ liệu dashboard</p>
          <Button onClick={fetchDashboardData} className="mt-4">
            Thử lại
          </Button>
        </div>
      </div>
    )
  }

  const { stats, recentActivity, pendingApprovals, recentComplaints } = dashboardData


  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Dashboard Quản trị</h1>
        <p className="text-muted-foreground">Tổng quan hệ thống và quản lý hoạt động</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng người dùng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1" />
              +{stats.monthlyGrowth}% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Talent</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTalents}</div>
            <p className="text-xs text-muted-foreground">{stats.pendingApprovals} đơn chờ duyệt</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn hàng</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Tất cả thời gian</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng doanh thu</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
                notation: "compact",
              }).format(stats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">Tất cả thời gian</p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khiếu nại chờ xử lý</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingComplaints}</div>
            <p className="text-xs text-orange-600">Cần xử lý ngay</p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đơn chờ duyệt</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.pendingApprovals}</div>
            <p className="text-xs text-blue-600">Talent & Business</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Hoạt động gần đây</CardTitle>
              <CardDescription>Theo dõi hoạt động của người dùng trên hệ thống</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4">
                    <Avatar>
                      <AvatarImage src={activity.avatar || "/placeholder.svg"} alt={activity.user} />
                      <AvatarFallback>{activity.user.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">{activity.user}</span> {activity.action}
                      </p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                    <Badge
                      variant={
                        activity.type === "complaint"
                          ? "destructive"
                          : activity.type === "talent_application"
                            ? "secondary"
                            : "default"
                      }
                    >
                      {activity.type === "new_user" && "Người dùng mới"}
                      {activity.type === "new_order" && "Đơn hàng mới"}
                      {activity.type === "complaint" && "Khiếu nại"}
                      {activity.type === "talent_application" && "Đơn ứng tuyển"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Khiếu nại gần đây</CardTitle>
              <CardDescription>Xử lý khiếu nại từ người dùng</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentComplaints.map((complaint) => (
                  <div key={complaint.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">#{complaint.orderId}</h3>
                        <Badge variant={complaint.status === "pending" ? "destructive" : "secondary"}>
                          {complaint.status === "pending" ? "Chờ xử lý" : "Đang điều tra"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {complaint.customer} khiếu nại {complaint.talent}
                      </p>
                      <p className="text-sm">{complaint.reason}</p>
                      <p className="text-xs text-muted-foreground mt-1">{complaint.submittedAt}</p>
                    </div>
                    <Button size="sm" asChild>
                      <Link href={`/admin/khieu-nai/${complaint.id}`}>Xử lý</Link>
                    </Button>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/admin/khieu-nai">Xem tất cả khiếu nại</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Đơn chờ duyệt</CardTitle>
              <CardDescription>Duyệt đơn ứng tuyển Talent & Business</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pendingApprovals.map((application) => (
                  <div key={application.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={application.avatar || "/placeholder.svg"} alt={application.name} />
                        <AvatarFallback>{application.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-sm">{application.name}</p>
                        <p className="text-xs text-muted-foreground">{application.category}</p>
                        <Badge variant="outline" className="text-xs">
                          {application.type}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleApprove(application.id, application.type.toLowerCase() as 'talent' | 'business')}
                      >
                        Duyệt
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleReject(application.id, application.type.toLowerCase() as 'talent' | 'business')}
                      >
                        Từ chối
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/admin/partner">Xem tất cả</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Hành động nhanh</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" asChild>
                <Link href="/admin/khieu-nai">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Xử lý khiếu nại
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/partner">
                  <Users className="mr-2 h-4 w-4" />
                  Duyệt đối tác
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/don-hang">
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Quản lý đơn hàng
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/nguoi-dung">
                  <Users className="mr-2 h-4 w-4" />
                  Quản lý người dùng
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Thống kê hệ thống</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Tỷ lệ hoàn thành đơn</span>
                  <span className="font-medium">94.2%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Đánh giá trung bình</span>
                  <span className="font-medium">4.6/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Thời gian phản hồi</span>
                  <span className="font-medium">18h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Tỷ lệ khiếu nại</span>
                  <span className="font-medium">2.1%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
