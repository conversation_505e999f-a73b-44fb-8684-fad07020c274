import { NextRequest, NextResponse } from "next/server";


class RoleName {
    static guest = "guest";
    static admin = "admin";
    static user = "user";
    static partner = "talent";
    static mod = "mod";
    static business = "business";
    static superadmin = "superadmin";
}

export function middleware(request: NextRequest) {
    const pathname = request.nextUrl.pathname;
    const user = request.cookies.get("user")?.value;
    let userObject = null;
    if (user) {
        try {
            userObject = JSON.parse(user);
            console.log("Parsed user object:", userObject);
            console.log("User roles from cookie:", userObject?.roles);
        } catch (error) {
            console.error("Error parsing user cookie:", error);
            console.log("Cookie content that failed to parse:", user);
        }
    }

    const roles = userObject?.roles || [];
    const roleNameList = roles?.map((role: any) => role.name);
    console.log("Roles array:", roles);
    console.log("Role names:", roleNameList);
    console.log("=== END MIDDLEWARE DEBUG ===");

    if (pathname.startsWith("/admin") && !roleNameList.includes(RoleName.admin) && !roleNameList.includes(RoleName.mod) && !roleNameList.includes(RoleName.superadmin)) {
        // Redirect to login if not admin, mod, or superadmin
        return NextResponse.redirect(new URL("/dang-nhap", request.url));
    }
    if (pathname.startsWith("/doi-tac") && !roleNameList.includes(RoleName.partner)) {
        // Redirect to login if not talent or business
        return NextResponse.redirect(new URL("/", request.url));
    }
    if ((pathname.startsWith("/chat") || pathname.startsWith("thong-bao")) && !user) {
        // Redirect to login if not authenticated
        return NextResponse.redirect(new URL("/dang-nhap", request.url));
    }
    if ((pathname.startsWith("/don-hang") || pathname.startsWith("/ho-so")) && !roleNameList.includes(RoleName.user)) {
        // Redirect to login if not user
        return NextResponse.redirect(new URL("/dang-nhap", request.url));
    }
    if (pathname.startsWith("/doanh-nghiep") && !roleNameList.includes(RoleName.business)) {
        // Redirect to login if not business
        return NextResponse.redirect(new URL("/", request.url));
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        "/admin/:path*",
        "/doi-tac/:path*",
        "/don-hang/:path*",
        "/ho-so/:path*",
        "/dang-xuat",
        "/chat/:path*",
        "/thong-bao/:path*",
        "/doanh-nghiep/:path*",
    ],
};