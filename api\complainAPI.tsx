import api from "./axios"
import { Paginator } from "./models/paginator"

export interface ComplaintOverview {
  id: number
  complaintId: string
  orderId: string
  user: {
    id: number
    name: string
    email: string
    avatarUrl?: string
  }
  talent: {
    id: number
    name: string
    email: string
    avatarUrl?: string
  }
  title: string
  description: string
  serviceName: string
  createdAt: string
  status: 'pending' | 'investigating' | 'resolved' | 'rejected'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  reasonExcerpt: string
  resolution?: string
  resolvedAt?: string
  resolvedById?: number
}

export interface AdminComplaintParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  priority?: string
  complaintType?: string
}

export const getComplaintAdmin = async (params: AdminComplaintParams): Promise<Paginator<ComplaintOverview>> => {
  const response = await api.get<Paginator<ComplaintOverview>>('/admin/complaints', { params })
  return response.data
}

export const updateComplaintStatus = async (complaintId: number, status: string): Promise<ComplaintOverview> => {
  const response = await api.patch<ComplaintOverview>(`/admin/complaints/${complaintId}/status`, { status })
  return response.data
}

export const updateComplaintPriority = async (complaintId: number, priority: string): Promise<ComplaintOverview> => {
  const response = await api.patch<ComplaintOverview>(`/admin/complaints/${complaintId}/priority`, { priority })
  return response.data
}

export const resolveComplaint = async (complaintId: number, resolution: string): Promise<ComplaintOverview> => {
  const response = await api.patch<ComplaintOverview>(`/admin/complaints/${complaintId}/resolve`, { resolution })
  return response.data
}

export const getComplaintDetails = async (complaintId: number): Promise<ComplaintOverview> => {
  const response = await api.get<ComplaintOverview>(`/admin/complaints/${complaintId}`)
  return response.data
}
